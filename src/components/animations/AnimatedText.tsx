"use client";

import { motion, useInView } from "framer-motion";
import { useRef, ReactNode } from "react";

interface AnimatedTextProps {
  children: ReactNode;
  className?: string;
  delay?: number;
  staggerChildren?: number;
  once?: boolean;
}

const AnimatedText = ({
  children,
  className = "",
  delay = 0,
  staggerChildren = 0.1,
  once = true,
}: AnimatedTextProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, { 
    once,
    margin: "-100px 0px -100px 0px"
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delay,
        staggerChildren,
        delayChildren: delay,
      },
    },
  };

  const childVariants = {
    hidden: { 
      opacity: 0, 
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.25, 0.25, 0.75],
      },
    },
  };

  // 如果children是字符串，将其分割为单词
  const renderAnimatedText = () => {
    if (typeof children === "string") {
      const words = children.split(" ");
      return words.map((word, index) => (
        <motion.span
          key={index}
          variants={childVariants}
          className="inline-block mr-2"
        >
          {word}
        </motion.span>
      ));
    }
    
    // 如果是React元素，直接返回
    return (
      <motion.div variants={childVariants}>
        {children}
      </motion.div>
    );
  };

  return (
    <motion.div
      ref={ref}
      variants={containerVariants}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      className={className}
    >
      {renderAnimatedText()}
    </motion.div>
  );
};

export default AnimatedText;
