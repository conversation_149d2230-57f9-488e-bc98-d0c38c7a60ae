"use client";

import { motion, useScroll, useTransform } from "framer-motion";
import { useRef, ReactNode } from "react";

interface ParallaxContainerProps {
  children: ReactNode;
  speed?: number;
  className?: string;
  offset?: [string, string];
}

const ParallaxContainer = ({ 
  children, 
  speed = 0.5, 
  className = "",
  offset = ["start end", "end start"]
}: ParallaxContainerProps) => {
  const ref = useRef<HTMLDivElement>(null);
  
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: offset as any,
  });

  const y = useTransform(scrollYProgress, [0, 1], ["0%", `${speed * 100}%`]);

  return (
    <div ref={ref} className={className}>
      <motion.div style={{ y }}>
        {children}
      </motion.div>
    </div>
  );
};

export default ParallaxContainer;
