"use client";

import { motion, useInView } from "framer-motion";
import { useRef, ReactNode, useEffect, useState } from "react";

interface ScrollTriggeredSectionProps {
  children: ReactNode;
  className?: string;
  delay?: number;
}

const ScrollTriggeredSection = ({
  children,
  className = "",
  delay = 0
}: ScrollTriggeredSectionProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const [hasScrolled, setHasScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      // 当滚动超过视口高度的70%时开始显示内容
      if (window.scrollY > window.innerHeight * 0.7) {
        setHasScrolled(true);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isInView = useInView(ref, {
    once: true,
    margin: "-100px 0px -100px 0px"
  });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={hasScrolled && isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{
        delay,
        duration: 0.8,
        ease: [0.25, 0.25, 0.25, 0.75]
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default ScrollTriggeredSection;
