import NewsLatterBox from "./NewsLatterBox";
import { ScrollReveal } from "../animations";

const Contact = () => {
  return (
    <section id="contact" className="overflow-hidden py-16 md:py-20 lg:py-28">
      <div className="container">
        <div className="-mx-4 flex flex-wrap">
          <div className="w-full px-4 lg:w-7/12 xl:w-8/12">
            <ScrollReveal direction="left" delay={0.1}>
              <div className="mb-12 rounded-xs bg-white px-8 py-11 shadow-three dark:bg-bg-color-dark sm:p-[55px] lg:mb-5 lg:px-8 xl:p-[55px]">
              <h2 className="mb-3 text-2xl font-bold text-black dark:text-white sm:text-3xl lg:text-2xl xl:text-3xl">
                联系我们
              </h2>
              <p className="mb-12 text-base font-medium text-body-color">
                如需了解更多产品信息或寻求专业建议，请联系我们的专业团队。
              </p>
              <form>
                <div className="-mx-4 flex flex-wrap">
                  <div className="w-full px-4 md:w-1/2">
                    <div className="mb-8">
                      <label
                        htmlFor="name"
                        className="mb-3 block text-sm font-medium text-dark dark:text-white"
                      >
                        您的姓名
                      </label>
                      <input
                        type="text"
                        placeholder="请输入您的姓名"
                        className="border-stroke w-full rounded-xs border bg-[#f8f8f8] px-6 py-3 text-base text-body-color outline-hidden focus:border-primary dark:border-transparent dark:bg-[#2C303B] dark:text-body-color-dark dark:shadow-two dark:focus:border-primary dark:focus:shadow-none"
                      />
                    </div>
                  </div>
                  <div className="w-full px-4 md:w-1/2">
                    <div className="mb-8">
                      <label
                        htmlFor="email"
                        className="mb-3 block text-sm font-medium text-dark dark:text-white"
                      >
                        您的邮箱
                      </label>
                      <input
                        type="email"
                        placeholder="请输入您的邮箱"
                        className="border-stroke w-full rounded-xs border bg-[#f8f8f8] px-6 py-3 text-base text-body-color outline-hidden focus:border-primary dark:border-transparent dark:bg-[#2C303B] dark:text-body-color-dark dark:shadow-two dark:focus:border-primary dark:focus:shadow-none"
                      />
                    </div>
                  </div>
                  <div className="w-full px-4">
                    <div className="mb-8">
                      <label
                        htmlFor="message"
                        className="mb-3 block text-sm font-medium text-dark dark:text-white"
                      >
                        您的留言
                      </label>
                      <textarea
                        name="message"
                        rows={5}
                        placeholder="请输入您的留言或咨询内容"
                        className="border-stroke w-full resize-none rounded-xs border bg-[#f8f8f8] px-6 py-3 text-base text-body-color outline-hidden focus:border-primary dark:border-transparent dark:bg-[#2C303B] dark:text-body-color-dark dark:shadow-two dark:focus:border-primary dark:focus:shadow-none"
                      ></textarea>
                    </div>
                  </div>
                  <div className="w-full px-4">
                    <button className="rounded-xs bg-primary px-9 py-4 text-base font-medium text-white shadow-submit duration-300 hover:bg-primary/90 dark:shadow-submit-dark">
                      提交咨询
                    </button>
                  </div>
                </div>
              </form>
              </div>
            </ScrollReveal>
          </div>
          <div className="w-full px-4 lg:w-5/12 xl:w-4/12">
            <ScrollReveal direction="right" delay={0.2}>
              <NewsLatterBox />
            </ScrollReveal>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
