import SectionTitle from "../Common/SectionTitle";
import SingleFeature from "./SingleFeature";
import featuresData from "./featuresData";
import { ScrollReveal } from "../animations";

const Features = () => {
  return (
    <>
      <section id="features" className="py-16 md:py-20 lg:py-28">
        <div className="container">
          <ScrollReveal direction="up" delay={0.1}>
            <SectionTitle
              title="产品特色"
              paragraph="未睐瞳产品系列涵盖VL功能性光学镜片和VF功能性镜框，为不同年龄段的青少年儿童提供专业的眼视光解决方案。"
              center
            />
          </ScrollReveal>

          <div className="grid grid-cols-1 gap-x-8 gap-y-14 md:grid-cols-2 lg:grid-cols-3">
            {featuresData.map((feature, index) => (
              <ScrollReveal
                key={feature.id}
                direction="up"
                delay={0.2 + index * 0.1}
              >
                <SingleFeature feature={feature} />
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default Features;
