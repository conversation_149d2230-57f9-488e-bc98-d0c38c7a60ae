import { Feature } from "@/types/feature";

const SingleFeature = ({ feature }: { feature: Feature }) => {
  const { icon, title, paragraph } = feature;
  return (
    <div className="w-full">
      <div className="group cursor-pointer">
        <div className="bg-primary/10 text-primary mb-10 flex h-[70px] w-[70px] items-center justify-center rounded-md transition-all duration-300 group-hover:bg-primary group-hover:text-white group-hover:scale-110">
          {icon}
        </div>
        <h3 className="mb-5 text-xl font-bold text-black sm:text-2xl lg:text-xl xl:text-2xl dark:text-white transition-colors duration-300 group-hover:text-primary dark:group-hover:text-primary">
          {title}
        </h3>
        <p className="text-body-color pr-[10px] text-base leading-relaxed font-medium transition-colors duration-300 group-hover:text-gray-600 dark:group-hover:text-gray-300">
          {paragraph}
        </p>
      </div>
    </div>
  );
};

export default SingleFeature;
