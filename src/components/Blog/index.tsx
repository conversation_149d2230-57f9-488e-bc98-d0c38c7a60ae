import SectionTitle from "../Common/SectionTitle";
import SingleBlog from "./SingleBlog";
import blogData from "./blogData";
import { ScrollReveal } from "../animations";

const Blog = () => {
  return (
    <section
      id="blog"
      className="bg-gray-light dark:bg-bg-color-dark py-16 md:py-20 lg:py-28"
    >
      <div className="container">
        <ScrollReveal direction="up" delay={0.1}>
          <SectionTitle
            title="最新资讯"
            paragraph="了解未睐瞳最新的产品动态、行业资讯和眼健康知识，为孩子的视力保护提供专业指导。"
            center
          />
        </ScrollReveal>

        <div className="grid grid-cols-1 gap-x-8 gap-y-10 md:grid-cols-2 md:gap-x-6 lg:gap-x-8 xl:grid-cols-3">
          {blogData.map((blog, index) => (
            <ScrollReveal key={blog.id} direction="up" delay={0.2 + index * 0.1}>
              <div className="w-full">
                <SingleBlog blog={blog} />
              </div>
            </ScrollReveal>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Blog;
