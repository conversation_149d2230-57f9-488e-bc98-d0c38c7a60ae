"use client";
import { useState } from "react";
import SectionTitle from "../Common/SectionTitle";
import OfferList from "./OfferList";
import PricingBox from "./PricingBox";

const Pricing = () => {
  const [isMonthly, setIsMonthly] = useState(true);

  return (
    <section id="pricing" className="relative z-10 py-16 md:py-20 lg:py-28">
      <div className="container">
        <SectionTitle
          title="产品价格"
          paragraph="未睐瞳为不同年龄段和需求的青少年儿童提供多样化的产品选择，价格透明合理，让每个孩子都能获得专业的眼健康解决方案。"
          center
          width="665px"
        />

        <div className="w-full">
          <div className="mb-8 flex justify-center md:mb-12 lg:mb-16">
            <span
              onClick={() => setIsMonthly(true)}
              className={`${
                isMonthly
                  ? "pointer-events-none text-primary"
                  : "text-dark dark:text-white"
              } mr-4 cursor-pointer text-base font-semibold`}
            >
              镜片
            </span>
            <div
              onClick={() => setIsMonthly(!isMonthly)}
              className="flex cursor-pointer items-center"
            >
              <div className="relative">
                <div className="h-5 w-14 rounded-full bg-[#1D2144] shadow-inner"></div>
                <div
                  className={`${
                    isMonthly ? "" : "translate-x-full"
                  } shadow-switch-1 absolute left-0 top-[-4px] flex h-7 w-7 items-center justify-center rounded-full bg-primary transition`}
                >
                  <span className="active h-4 w-4 rounded-full bg-white"></span>
                </div>
              </div>
            </div>
            <span
              onClick={() => setIsMonthly(false)}
              className={`${
                isMonthly
                  ? "text-dark dark:text-white"
                  : "pointer-events-none text-primary"
              } ml-4 cursor-pointer text-base font-semibold`}
            >
              镜框
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-x-8 gap-y-10 md:grid-cols-2 lg:grid-cols-3">
          <PricingBox
            packageName={isMonthly ? "弱视远视专用镜片" : "防过敏新材镜框"}
            price={isMonthly ? "咨询" : "528"}
            duration=""
            subtitle={isMonthly ? "适合0-8岁弱视/远视患儿，曲面AI优化设计" : "适合年龄较小患儿，法国Arkema G850创新材料"}
          >
            <OfferList text={isMonthly ? "曲面AI优化设计" : "防过敏新材料"} status="active" />
            <OfferList text={isMonthly ? "克服中心扭曲色散" : "鼻托可调"} status="active" />
            <OfferList text={isMonthly ? "提高镜片解析度" : "镜腿五档可调"} status="active" />
            <OfferList text={isMonthly ? "扩大佩戴者视野" : "皮肤接触安全"} status="active" />
            <OfferList text={isMonthly ? "加速弱视治愈" : "适配各类鼻梁结构"} status="active" />
            <OfferList text={isMonthly ? "临床验证效果" : "耐用性强"} status="active" />
          </PricingBox>
          <PricingBox
            packageName={isMonthly ? "远视储备不足专用镜片" : "榫卯结构镜框"}
            price={isMonthly ? "咨询" : "628"}
            duration=""
            subtitle={isMonthly ? "适合6-12岁远视储备不足儿童，延缓眼轴增长" : "适合6-18岁，无螺丝全模块化设计"}
          >
            <OfferList text={isMonthly ? "中央区平光设计" : "无螺丝结构"} status="active" />
            <OfferList text={isMonthly ? "周边远视性离焦" : "全模块化设计"} status="active" />
            <OfferList text={isMonthly ? "梯度光度变化" : "配件可拆卸更换"} status="active" />
            <OfferList text={isMonthly ? "延缓眼轴增长" : "五档可调镜腿"} status="active" />
            <OfferList text={isMonthly ? "舒适近用体验" : "零压鼻托"} status="active" />
            <OfferList text={isMonthly ? "复旦医院验证" : "适配离焦镜片"} status="active" />
          </PricingBox>
          <PricingBox
            packageName={isMonthly ? "近视管理专用镜片" : "TR半钛稳态镜框"}
            price={isMonthly ? "咨询" : "588"}
            duration=""
            subtitle={isMonthly ? "适合6-18岁近视患者，非对称周边离焦设计" : "适合14岁以上，超弹柔韧硅胶钛脚"}
          >
            <OfferList text={isMonthly ? "非对称周边离焦" : "超弹柔韧硅胶钛脚"} status="active" />
            <OfferList text={isMonthly ? "渐进多焦融合" : "马鞍硅胶鼻托"} status="active" />
            <OfferList text={isMonthly ? "良好中央视力" : "稳定性强"} status="active" />
            <OfferList text={isMonthly ? "优异视觉体验" : "跑跳不易掉"} status="active" />
            <OfferList text={isMonthly ? "适应性佳" : "时尚潮流设计"} status="active" />
            <OfferList text={isMonthly ? "首副镜片最佳选择" : "满足不同年龄需求"} status="active" />
          </PricingBox>
        </div>
      </div>

      <div className="absolute bottom-0 left-0 z-[-1]">
        <svg
          width="239"
          height="601"
          viewBox="0 0 239 601"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            opacity="0.3"
            x="-184.451"
            y="600.973"
            width="196"
            height="541.607"
            rx="2"
            transform="rotate(-128.7 -184.451 600.973)"
            fill="url(#paint0_linear_93:235)"
          />
          <rect
            opacity="0.3"
            x="-188.201"
            y="385.272"
            width="59.7544"
            height="541.607"
            rx="2"
            transform="rotate(-128.7 -188.201 385.272)"
            fill="url(#paint1_linear_93:235)"
          />
          <defs>
            <linearGradient
              id="paint0_linear_93:235"
              x1="-90.1184"
              y1="420.414"
              x2="-90.1184"
              y2="1131.65"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#4A6CF7" />
              <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_93:235"
              x1="-159.441"
              y1="204.714"
              x2="-159.441"
              y2="915.952"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#4A6CF7" />
              <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </section>
  );
};

export default Pricing;
